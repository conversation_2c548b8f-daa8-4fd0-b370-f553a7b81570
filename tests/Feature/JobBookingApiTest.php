<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\JobBooking;
use App\Models\Bid;
use App\Enums\RoleEnum;
use App\Enums\JobBookingStatusEnum;
use App\Enums\BidStatusEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class JobBookingApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $consumer;
    protected $provider;
    protected $jobBooking;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->consumer = User::factory()->create();
        $this->consumer->assignRole(RoleEnum::CONSUMER);
        
        $this->provider = User::factory()->create();
        $this->provider->assignRole(RoleEnum::PROVIDER);
        
        // Create a test job booking
        $this->jobBooking = JobBooking::factory()->create([
            'user_id' => $this->consumer->id,
            'status' => JobBookingStatusEnum::OPEN
        ]);
    }

    /** @test */
    public function consumer_can_view_their_job_bookings()
    {
        $response = $this->actingAs($this->consumer, 'api')
            ->getJson('/api/job-bookings');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'jobId',
                        'status',
                        'jobType',
                        'service',
                        'schedule',
                        'location',
                        'contact'
                    ]
                ]
            ]);
    }

    /** @test */
    public function consumer_can_accept_a_bid()
    {
        // Create a bid for the job booking
        $bid = Bid::factory()->create([
            'job_booking_id' => $this->jobBooking->id,
            'provider_id' => $this->provider->id,
            'status' => BidStatusEnum::REQUESTED,
            'amount' => 100.00
        ]);

        $response = $this->actingAs($this->consumer, 'api')
            ->postJson("/api/job-bookings/{$this->jobBooking->job_uuid}/accept-bid", [
                'bid_id' => $bid->id,
                'notes' => 'Looking forward to working with you!'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Bid accepted successfully'
            ]);

        // Verify the bid was accepted
        $this->assertDatabaseHas('bids', [
            'id' => $bid->id,
            'status' => BidStatusEnum::ACCEPTED
        ]);

        // Verify the job booking status was updated
        $this->assertDatabaseHas('job_bookings', [
            'id' => $this->jobBooking->id,
            'status' => JobBookingStatusEnum::ASSIGNED
        ]);

        // Verify a Job record was created
        $this->assertDatabaseHas('business_jobs', [
            'job_booking_id' => $this->jobBooking->id,
            'bid_id' => $bid->id,
            'customer_id' => $this->consumer->id,
            'provider_id' => $this->provider->id,
            'status' => 'assigned',
            'agreed_amount' => 100.00
        ]);
    }

    /** @test */
    public function provider_cannot_accept_bids()
    {
        $bid = Bid::factory()->create([
            'job_booking_id' => $this->jobBooking->id,
            'provider_id' => $this->provider->id,
            'status' => BidStatusEnum::REQUESTED
        ]);

        $response = $this->actingAs($this->provider, 'api')
            ->postJson("/api/job-bookings/{$this->jobBooking->job_uuid}/accept-bid", [
                'bid_id' => $bid->id
            ]);

        $response->assertStatus(403); // Forbidden
    }

    /** @test */
    public function consumer_can_delete_pending_job_booking()
    {
        $pendingJob = JobBooking::factory()->create([
            'user_id' => $this->consumer->id,
            'status' => JobBookingStatusEnum::PENDING
        ]);

        $response = $this->actingAs($this->consumer, 'api')
            ->deleteJson("/api/job-bookings/{$pendingJob->job_uuid}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Job booking deleted successfully'
            ]);

        $this->assertSoftDeleted('job_bookings', [
            'id' => $pendingJob->id
        ]);
    }

    /** @test */
    public function consumer_cannot_delete_assigned_job_booking()
    {
        $assignedJob = JobBooking::factory()->create([
            'user_id' => $this->consumer->id,
            'status' => JobBookingStatusEnum::ASSIGNED
        ]);

        $response = $this->actingAs($this->consumer, 'api')
            ->deleteJson("/api/job-bookings/{$assignedJob->job_uuid}");

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'error' => [
                    'code' => 'CANNOT_DELETE',
                    'message' => 'Only pending job bookings can be deleted'
                ]
            ]);
    }

    /** @test */
    public function accept_bid_validates_required_fields()
    {
        $response = $this->actingAs($this->consumer, 'api')
            ->postJson("/api/job-bookings/{$this->jobBooking->job_uuid}/accept-bid", []);

        $response->assertStatus(422);
    }

    /** @test */
    public function accept_bid_validates_bid_exists()
    {
        $response = $this->actingAs($this->consumer, 'api')
            ->postJson("/api/job-bookings/{$this->jobBooking->job_uuid}/accept-bid", [
                'bid_id' => 99999 // Non-existent bid
            ]);

        $response->assertStatus(422);
    }
}
