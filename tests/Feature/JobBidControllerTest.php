<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\JobBooking;
use App\Models\Bid;
use App\Enums\RoleEnum;
use App\Enums\JobBookingStatusEnum;
use App\Enums\BidStatusEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class JobBidControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $consumer;
    protected $provider;
    protected $jobBooking;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->consumer = User::factory()->create();
        $this->consumer->assignRole(RoleEnum::CONSUMER);
        
        $this->provider = User::factory()->create();
        $this->provider->assignRole(RoleEnum::PROVIDER);
        
        // Create a test job booking
        $this->jobBooking = JobBooking::factory()->create([
            'user_id' => $this->consumer->id,
            'status' => JobBookingStatusEnum::OPEN
        ]);
    }

    /** @test */
    public function provider_can_place_bid_on_open_job_booking()
    {
        $response = $this->actingAs($this->provider, 'api')
            ->postJson("/api/job-bookings/{$this->jobBooking->id}/bids", [
                'amount' => 150.00,
                'description' => 'I can complete this job efficiently.',
                'estimated_completion_time' => now()->addDays(3)->toDateTimeString()
            ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Bid placed successfully.'
            ])
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'job_booking_id',
                    'provider_id',
                    'amount',
                    'description',
                    'status',
                    'estimated_completion_time'
                ]
            ]);

        $this->assertDatabaseHas('bids', [
            'job_booking_id' => $this->jobBooking->id,
            'provider_id' => $this->provider->id,
            'amount' => 150.00,
            'status' => BidStatusEnum::REQUESTED
        ]);
    }

    /** @test */
    public function anyone_can_view_bids_for_job_booking()
    {
        $bid = Bid::factory()->create([
            'job_booking_id' => $this->jobBooking->id,
            'provider_id' => $this->provider->id,
            'status' => BidStatusEnum::REQUESTED
        ]);

        $response = $this->actingAs($this->consumer, 'api')
            ->getJson("/api/job-bookings/{$this->jobBooking->id}/bids");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ])
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'job_booking_id',
                        'provider_id',
                        'amount',
                        'description',
                        'status'
                    ]
                ],
                'meta' => [
                    'total_bids',
                    'lowest_bid',
                    'highest_bid',
                    'average_bid'
                ]
            ]);
    }

    /** @test */
    public function provider_can_update_their_own_bid()
    {
        $bid = Bid::factory()->create([
            'job_booking_id' => $this->jobBooking->id,
            'provider_id' => $this->provider->id,
            'status' => BidStatusEnum::REQUESTED,
            'amount' => 100.00
        ]);

        $response = $this->actingAs($this->provider, 'api')
            ->putJson("/api/job-bids/{$bid->id}", [
                'amount' => 120.00,
                'description' => 'Updated bid description.'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Bid updated successfully.'
            ]);

        $this->assertDatabaseHas('bids', [
            'id' => $bid->id,
            'amount' => 120.00
        ]);
    }

    /** @test */
    public function provider_can_withdraw_their_own_bid()
    {
        $bid = Bid::factory()->create([
            'job_booking_id' => $this->jobBooking->id,
            'provider_id' => $this->provider->id,
            'status' => BidStatusEnum::REQUESTED
        ]);

        $response = $this->actingAs($this->provider, 'api')
            ->deleteJson("/api/job-bids/{$bid->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Bid withdrawn successfully.'
            ]);

        $this->assertDatabaseHas('bids', [
            'id' => $bid->id,
            'status' => BidStatusEnum::WITHDRAWN
        ]);
    }
}
