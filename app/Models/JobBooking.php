<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;
use App\Services\ProjectCodeGenerator;
use App\Enums\JobBookingStatusEnum;
use App\Events\JobBookingStatusChangedEvent;
use App\Events\JobBookingBidAcceptedEvent;

class JobBooking extends Model
{
    use HasFactory;

    protected $table = 'job_bookings';

    protected $fillable = [
        'job_uuid',
        'project_code',
        'job_type',
        'property_type',
        'service_category',
        'service_tasks',
        'description',
        'schedule_date',
        'time_preference',
        'frequency',
        'recurring_frequency',
        'address',
        'city',
        'state',
        'zip_code',
        'contact_name',
        'contact_email',
        'contact_phone',
        'status',
        'user_id'
    ];

    protected $casts = [
        'schedule_date' => 'date',
        'service_tasks' => 'array',
    ];

    /**
     * Get the service tasks
     * 
     * @return array
     */
    public function getServiceTasksAttribute($value)
    {
        if (is_string($value)) {
            return json_decode($value, true) ?: [];
        }
        return is_array($value) ? $value : [];
    }

    public static function boot()
    {
        parent::boot();
        
        static::creating(function ($job) {
            $job->job_uuid = (string) Str::uuid();
            
            // Generate project code
            $projectCodeGenerator = new ProjectCodeGenerator();
            $job->project_code = $projectCodeGenerator->generateProjectCode($job->job_uuid);
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function assets()
    {
        return $this->belongsToMany(Asset::class, 'job_booking_assets')
                    ->withTimestamps();
    }

    /**
     * Get the bids for this job booking
     */
    public function bids(): HasMany
    {
        return $this->hasMany(Bid::class, 'job_booking_id');
    }

    /**
     * Get the bookings for this job booking
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'job_booking_id');
    }

    /**
     * Get the jobs for this job booking
     */
    public function jobs(): HasMany
    {
        return $this->hasMany(Job::class, 'job_booking_id');
    }

    /**
     * Get the assigned job for this job booking
     */
    public function assignedJob()
    {
        return $this->jobs()->first();
    }

    /**
     * Get the accepted bid for this job booking
     */
    public function acceptedBid()
    {
        return $this->bids()->where('status', 'accepted')->first();
    }

    /**
     * Get pending bids for this job booking
     */
    public function pendingBids(): HasMany
    {
        return $this->bids()->where('status', 'requested');
    }

    /**
     * Get the assigned booking for this job booking
     */
    public function assignedBooking()
    {
        return $this->bookings()->first();
    }

    // ==================== STATUS HELPER METHODS ====================

    /**
     * Check if job booking allows bidding
     */
    public function allowsBidding(): bool
    {
        return JobBookingStatusEnum::allowsBidding($this->status);
    }

    /**
     * Check if job booking is in final status
     */
    public function isFinal(): bool
    {
        return JobBookingStatusEnum::isFinal($this->status);
    }

    /**
     * Check if job booking is pending
     */
    public function isPending(): bool
    {
        return $this->status === JobBookingStatusEnum::PENDING;
    }

    /**
     * Check if job booking is open for bids
     */
    public function isOpen(): bool
    {
        return $this->status === JobBookingStatusEnum::OPEN;
    }

    /**
     * Check if job booking is assigned
     */
    public function isAssigned(): bool
    {
        return $this->status === JobBookingStatusEnum::ASSIGNED;
    }

    /**
     * Check if job booking is in progress
     */
    public function isInProgress(): bool
    {
        return $this->status === JobBookingStatusEnum::IN_PROGRESS;
    }

    /**
     * Check if job booking is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === JobBookingStatusEnum::COMPLETED;
    }

    /**
     * Check if job booking is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === JobBookingStatusEnum::CANCELLED;
    }

    /**
     * Check if job booking is expired
     */
    public function isExpired(): bool
    {
        return $this->status === JobBookingStatusEnum::EXPIRED;
    }

    // ==================== WORKFLOW TRANSITION METHODS ====================

    /**
     * Mark job booking as open for bidding
     */
    public function markAsOpen(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::OPEN)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::OPEN;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Assign job booking to a provider via accepted bid
     */
    public function markAsAssigned($bidId = null): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::ASSIGNED)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::ASSIGNED;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Mark job booking as in progress
     */
    public function markAsInProgress(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::IN_PROGRESS)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::IN_PROGRESS;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Mark job booking as completed
     */
    public function markAsCompleted(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::COMPLETED)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::COMPLETED;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Cancel job booking
     */
    public function cancel(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::CANCELLED)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::CANCELLED;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    /**
     * Mark job booking as expired
     */
    public function markAsExpired(): bool
    {
        if (!$this->canTransitionTo(JobBookingStatusEnum::EXPIRED)) {
            return false;
        }

        $oldStatus = $this->status;
        $this->status = JobBookingStatusEnum::EXPIRED;

        if ($this->save()) {
            event(new JobBookingStatusChangedEvent($this, $oldStatus, $this->status));
            return true;
        }

        return false;
    }

    // ==================== WORKFLOW VALIDATION METHODS ====================

    /**
     * Check if job booking can transition to given status
     */
    public function canTransitionTo(string $newStatus): bool
    {
        $allowedTransitions = JobBookingStatusEnum::getNextStatuses($this->status);
        return in_array($newStatus, $allowedTransitions);
    }

    /**
     * Check if job booking can accept bids
     */
    public function canAcceptBids(): bool
    {
        return $this->allowsBidding() && $this->bids()->where('status', 'requested')->exists();
    }

    /**
     * Check if job booking can be cancelled
     */
    public function canCancel(): bool
    {
        return !$this->isFinal();
    }

    /**
     * Check if job booking can be marked as in progress
     */
    public function canMarkAsInProgress(): bool
    {
        return $this->isAssigned();
    }

    /**
     * Check if job booking can be completed
     */
    public function canComplete(): bool
    {
        return $this->isInProgress();
    }

    /**
     * Check if job booking can be expired
     */
    public function canExpire(): bool
    {
        return $this->isOpen();
    }

    /**
     * Get workflow status display name
     */
    public function getStatusDisplayName(): string
    {
        return match ($this->status) {
            JobBookingStatusEnum::PENDING => 'Pending Review',
            JobBookingStatusEnum::OPEN => 'Open for Bids',
            JobBookingStatusEnum::ASSIGNED => 'Assigned to Provider',
            JobBookingStatusEnum::IN_PROGRESS => 'Work in Progress',
            JobBookingStatusEnum::COMPLETED => 'Completed',
            JobBookingStatusEnum::CANCELLED => 'Cancelled',
            JobBookingStatusEnum::EXPIRED => 'Expired',
            default => 'Unknown Status',
        };
    }

    /**
     * Get available actions for current status
     */
    public function getAvailableActions(): array
    {
        $actions = [];

        if ($this->canTransitionTo(JobBookingStatusEnum::OPEN)) {
            $actions[] = 'open_for_bidding';
        }

        if ($this->canAcceptBids()) {
            $actions[] = 'accept_bid';
        }

        if ($this->canMarkAsInProgress()) {
            $actions[] = 'start_work';
        }

        if ($this->canComplete()) {
            $actions[] = 'complete';
        }

        if ($this->canCancel()) {
            $actions[] = 'cancel';
        }

        if ($this->canExpire()) {
            $actions[] = 'expire';
        }

        return $actions;
    }

    /**
     * Accept a bid for this job booking
     */
    public function acceptBid($bidId, $notes = null): bool
    {
        if (!$this->canAcceptBids()) {
            return false;
        }

        $bid = $this->bids()->find($bidId);
        if (!$bid || $bid->status !== 'requested') {
            return false;
        }

        // Use database transaction to ensure data consistency
        return \DB::transaction(function () use ($bid, $bidId, $notes) {
            // Accept the bid
            $bid->status = 'accepted';
            $bid->save();

            // Reject all other bids
            $this->bids()->where('id', '!=', $bidId)->where('status', 'requested')->update(['status' => 'rejected']);

            // Create a Job record to link the booking and bid
            $job = Job::create([
                'job_booking_id' => $this->id,
                'bid_id' => $bid->id,
                'customer_id' => $this->user_id,
                'provider_id' => $bid->provider_id,
                'status' => 'assigned',
                'agreed_amount' => $bid->amount,
                'estimated_completion_time' => $bid->estimated_completion_time,
                'notes' => $notes
            ]);

            // Fire bid accepted event
            event(new JobBookingBidAcceptedEvent($this, $bid));

            // Mark job booking as assigned
            $this->markAsAssigned($bidId);

            return true;
        });
    }

    // ==================== QUERY SCOPES ====================

    /**
     * Scope to filter by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get open job bookings
     */
    public function scopeOpen($query)
    {
        return $query->where('status', JobBookingStatusEnum::OPEN);
    }

    /**
     * Scope to get pending job bookings
     */
    public function scopePending($query)
    {
        return $query->where('status', JobBookingStatusEnum::PENDING);
    }

    /**
     * Scope to get assigned job bookings
     */
    public function scopeAssigned($query)
    {
        return $query->where('status', JobBookingStatusEnum::ASSIGNED);
    }

    /**
     * Scope to get active job bookings (not final)
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', JobBookingStatusEnum::getActiveStatuses());
    }

    /**
     * Scope to get completed job bookings
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', JobBookingStatusEnum::COMPLETED);
    }

    /**
     * Scope to get cancelled job bookings
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', JobBookingStatusEnum::CANCELLED);
    }

    /**
     * Scope to get job bookings that allow bidding
     */
    public function scopeAllowsBidding($query)
    {
        return $query->whereIn('status', [JobBookingStatusEnum::PENDING, JobBookingStatusEnum::OPEN]);
    }

    /**
     * Scope to get job bookings with pending bids
     */
    public function scopeWithPendingBids($query)
    {
        return $query->whereHas('bids', function ($query) {
            $query->where('status', 'requested');
        });
    }

    /**
     * Scope to get job bookings by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get job bookings by date range
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('schedule_date', [$startDate, $endDate]);
    }
}